package http

import (
	"fmt"
	"io"
	"log/slog"
	"os"
	"time"

	"ziaacademy-backend/db"
	"ziaacademy-backend/docs"
	"ziaacademy-backend/internal/middleware"

	"github.com/gin-gonic/gin"
	"github.com/naughtygopher/errors"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Config holds all the configuration required to start the HTTP server
type Config struct {
	Host string
	Port uint16

	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	DialTimeout  time.Duration

	TemplatesBasePath string
	EnableAccessLog   bool
}

// Handlers struct has all the dependencies required for HTTP handlers
type Handlers struct {
	db db.Server
}

func NewHandlers(db db.Server) *Handlers {
	return &Handlers{
		db: db,
	}
}

//  @title			ZIA Academy App
//	@version		1.0
//	@description	Backend server for ZIA Academy.
//	@termsOfService	http://swagger.io/terms/

//	@contact.name	API Support
//	@contact.url	http://www.swagger.io/support
//	@contact.email	<EMAIL>

//	@license.name	Apache 2.0
//	@license.url	http://www.apache.org/licenses/LICENSE-2.0.html

//	@host		localhost:443
//	@BasePath	/api/

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func SetupRouter(svr db.Server, logFile *os.File, skipAuth bool) *gin.Engine {
	slog.Info("Setting up HTTP router", "skip_auth", skipAuth)

	gin.DefaultWriter = io.MultiWriter(logFile)
	router := gin.Default()

	// Add request logging middleware
	router.Use(middleware.RequestLoggingMiddleware())

	h := NewHandlers(svr)

	slog.Info("Configuring public routes")
	open := router.Group("api")
	open.POST("/students", h.CreateStudent)
	open.POST("/students/send-verification-code", h.SendVerificationCode)
	open.POST("/login", h.Login)

	slog.Info("Configuring protected routes", "auth_enabled", !skipAuth)
	protected := router.Group("api")
	if !skipAuth {
		protected.Use(middleware.JwtAuthMiddleware())
	}

	docs.SwaggerInfo.BasePath = "/api"

	protected.POST("/courses", h.CreateCourse)
	protected.POST("/courses/:course_id/tests/:test_id", h.AssociateTestWithCourse)
	protected.POST("/subjects", h.CreateSubject)
	protected.POST("/chapters", h.CreateChapter)
	protected.POST("/topics", h.CreateTopic)
	protected.POST("/questions", h.CreateQuestion)
	protected.POST("/videos", h.AddVideo)
	protected.POST("/studymaterials", h.AddStudyMaterial)
	protected.POST("/formula-cards", h.CreateFormulaCards)
	protected.POST("/previous-year-papers", h.CreatePreviousYearPapers)
	protected.GET("/subjects", h.GetSubjects)
	protected.GET("/chapters", h.GetChapters)
	protected.GET("/topics", h.GetTopics)
	protected.GET("/formula-cards", h.GetFormulaCards)
	protected.GET("/previous-year-papers", h.GetAllPreviousYearPapersOrganizedByExamType)
	protected.GET("/courses", h.GetCourses)
	protected.GET("/content", h.GetContent)
	protected.POST("/users/password", h.UpdatePassword)
	protected.GET("/questions", h.GetQuestions)
	protected.POST("/enroll/:course_id", h.EnrollInCourse)
	protected.POST("/admins", h.CreateAdmin)

	// Test-related routes
	protected.POST("/section-types", h.CreateSectionType)
	protected.GET("/section-types", h.GetSectionTypes)
	protected.POST("/test-types", h.CreateTestType)
	protected.GET("/test-types", h.GetTestTypes)
	protected.POST("/tests", h.CreateTest)
	protected.GET("/tests", h.GetTests)
	protected.POST("/tests/:test_id/questions", h.AddQuestionsToTest)
	protected.DELETE("/tests/:test_id/questions", h.RemoveQuestionsFromTest)

	// Test response routes
	protected.POST("/test-responses", h.RecordTestResponses)
	protected.GET("/test-responses/:test_id", h.GetStudentTestResponses)
	protected.POST("/test-responses/evaluate", h.EvaluateTestResponses)
	protected.GET("/test-responses/rankings/:test_id", h.GetTestRankings)

	// Comment routes
	protected.POST("/comments", h.AddComment)
	protected.POST("/responses", h.AddResponse)
	protected.GET("/comments", h.GetComments)

	// Transaction routes
	protected.POST("/transactions", h.CreateTransaction)
	protected.GET("/transactions", h.GetTransactions)
	protected.GET("/transactions/:id", h.GetTransactionByID)
	protected.PUT("/transactions/:id/status", h.UpdateTransactionStatus)

	// use ginSwagger middleware to serve the API docs
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	slog.Info("HTTP router setup completed",
		"swagger_enabled", true,
		"auth_middleware_enabled", !skipAuth,
	)
	return router
}

func StartServer(svr db.Server, httpCfg *Config,
	logFile *os.File,
	sslCert, sslKey string,
	fatalErr chan<- error) *gin.Engine {

	slog.Info("Starting HTTP server",
		"host", httpCfg.Host,
		"port", httpCfg.Port,
		"ssl_cert", sslCert,
		"ssl_key", sslKey,
	)

	router := SetupRouter(svr, logFile, false)
	go func() {
		defer func() {
			rec := recover()
			if rec != nil {
				slog.Error("HTTP server panic recovered", "panic", rec)
				fatalErr <- errors.New(fmt.Sprintf("%+v", rec))
			}
		}()

		serverAddr := fmt.Sprintf("%s:%d", httpCfg.Host, httpCfg.Port)
		slog.Info("HTTP server listening", "address", serverAddr)

		err := router.RunTLS(serverAddr, sslCert, sslKey)
		if err != nil {
			slog.Error("HTTP server failed to start",
				"address", serverAddr,
				"error", err.Error(),
			)
			fatalErr <- errors.Wrap(err, "failed to start HTTP server")
		}
	}()

	slog.Info("HTTP server startup initiated successfully")
	return router
}
